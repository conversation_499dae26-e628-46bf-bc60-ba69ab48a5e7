<?php
return [
    'frontend' => [
        // Virtual Route Handler - DISABLED to prevent interference with normal site functionality
        // TODO: Re-enable with proper filtering once the core issue is resolved
        // 'landing-pages/virtual-route-handler' => [
        //     'target' => \Bgs\LandingPages\Middleware\VirtualRouteHandler::class,
        //     'after' => [
        //         'typo3/cms-frontend/site',
        //         'typo3/cms-frontend/authentication',
        //         'typo3/cms-frontend/backend-user-authentication',
        //     ],
        //     'before' => [
        //         'typo3/cms-frontend/page-resolver',
        //     ],
        // ],
        // Dynamic Arguments Middleware - Disabled as it interferes with TypoScript processing
        // 'landing-pages/dynamic-arguments' => [
        //     'target' => \Bgs\LandingPages\Middleware\DynamicArgumentsMiddleware::class,
        //     'after' => [
        //         'typo3/cms-frontend/page-argument-validator',
        //     ],
        //     'before' => [
        //         'typo3/cms-frontend/tsfe',
        //     ],
        // ],
    ],
];
