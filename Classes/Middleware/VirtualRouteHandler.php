<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Middleware;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Bgs\LandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Log\LogManager;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Routing\SiteRouteResult;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Virtual Route Handler Middleware
 *
 * This middleware runs BEFORE PageResolver to intercept virtual routes
 * and modify the request to point to the template page instead.
 * This prevents PageResolver from rejecting virtual routes with 404
 * and simplifies processing by making the request point directly to the template page.
 */
class VirtualRouteHandler implements MiddlewareInterface
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $logger = GeneralUtility::makeInstance(LogManager::class)->getLogger(__CLASS__);

        $site = $request->getAttribute('site');
        if (!$site instanceof Site) {
            return $handler->handle($request);
        }

        $path = $request->getUri()->getPath();

        $logger->info('VirtualRouteHandler: Initial path check', [
            'fullPath' => $path,
            'uri' => (string)$request->getUri(),
            'basePath' => $site->getBase()->getPath()
        ]);

        // SAFETY CHECK: Only process if this looks like a potential virtual route
        // This prevents interference with normal site pages
        if (!$this->isVirtualRouteCandidate($path, $site)) {
            $logger->info('VirtualRouteHandler: Not a virtual route candidate, passing through');
            return $handler->handle($request);
        }

        $logger->info('VirtualRouteHandler: Processing potential virtual route', [
            'uri' => (string)$request->getUri(),
            'path' => $path,
            'site' => $site->getIdentifier(),
            'basePath' => $site->getBase()->getPath()
        ]);

        // Detect virtual route
        $virtualRouteMatch = $this->virtualRouteService->detectVirtualRoute($path, $site);

        $logger->info('VirtualRouteHandler: Route detection result', [
            'hasMatch' => $virtualRouteMatch !== null,
            'matchData' => $virtualRouteMatch ? array_keys($virtualRouteMatch) : null
        ]);

        if ($virtualRouteMatch) {
            // Clear any previous virtual route state only when we have a match
            $this->virtualRouteService->clearVirtualRoute();
            // Load template page data to get its slug
            $templatePageUid = $virtualRouteMatch['templatePageUid'];
            $templatePage = $this->virtualRouteService->loadTemplatePage($templatePageUid);

            if (!$templatePage) {
                return $handler->handle($request);
            }

            // Use template page path instead of landing page path
            $templatePagePath = $this->getTemplatePagePath($templatePage, $site);

            // Create VirtualRouteContext entity with all necessary data
            $virtualRouteContext = VirtualRouteContext::createVirtual(
                $virtualRouteMatch['landingPage'],
                $templatePage,
                $virtualRouteMatch['flightRoute'],
                $virtualRouteMatch['originalPath'],
                $virtualRouteMatch['routeSlug'],
                $templatePageUid,
                $templatePagePath
            );

            // Store virtual route context for later processing by PSR-14 events
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);

            // Create a new URI pointing to the template page (REQUIRED for virtual routes to work)
            // Add a unique parameter to ensure each virtual route gets its own cache entry
            $routeSlug = $virtualRouteMatch['flightRoute']['route_slug'] ?? '';
            $cacheParam = 'route=' . urlencode($routeSlug);
            $existingQuery = $request->getUri()->getQuery();
            $newQuery = $existingQuery ? $existingQuery . '&' . $cacheParam : $cacheParam;

            $newUri = $request->getUri()
                ->withPath($templatePagePath)
                ->withQuery($newQuery);

            $modifiedRequest = $request->withUri($newUri);
            // Also update the routing attribute to match the new path
            $previousResult = $request->getAttribute('routing');
            if ($previousResult instanceof SiteRouteResult) {
                // Create a new SiteRouteResult with the modified tail
                $basePath = rtrim($site->getBase()->getPath(), '/');

                // Only remove base path from the beginning of the template page path
                $newTail = $templatePagePath;
                if (strpos($templatePagePath, $basePath) === 0) {
                    $newTail = substr($templatePagePath, strlen($basePath));
                }
                $newTail = ltrim($newTail, '/');
                $newRouteResult = new SiteRouteResult(
                    $newUri,
                    $previousResult->getSite(),
                    $previousResult->getLanguage(),
                    $newTail
                );
                $modifiedRequest = $modifiedRequest->withAttribute('routing', $newRouteResult);
            }

            // Set single organized attribute instead of multiple scattered ones
            $modifiedRequest = $modifiedRequest->withAttribute('landing-pages.virtual_route_context', $virtualRouteContext);
            $response = $handler->handle($modifiedRequest);

            return $response;
        }

        return $handler->handle($request);
    }

    /**
     * Safely determine if a path could be a virtual route candidate
     * This method uses multiple checks to prevent interference with normal site pages
     */
    private function isVirtualRouteCandidate(string $path, Site $site): bool
    {
        // Remove site base path from the path for analysis
        $basePath = rtrim($site->getBase()->getPath(), '/');
        $relativePath = $path;

        if ($basePath && strpos($path, $basePath) === 0) {
            $relativePath = substr($path, strlen($basePath));
        }
        $relativePath = trim($relativePath, '/');

        $pathParts = array_filter(explode('/', $relativePath));

        // Debug logging
        $logger = GeneralUtility::makeInstance(LogManager::class)->getLogger(__CLASS__);
        $logger->info('VirtualRouteCandidate check', [
            'originalPath' => $path,
            'basePath' => $basePath,
            'relativePath' => $relativePath,
            'pathParts' => $pathParts,
            'pathPartsCount' => count($pathParts)
        ]);

        // Must have at least 3 segments for virtual routes: flights/poleti/route-slug
        if (count($pathParts) < 3) {
            $logger->info('Not enough path segments', ['count' => count($pathParts)]);
            return false;
        }

        // Check if it matches the expected pattern: flights/poleti/route-slug
        if (count($pathParts) >= 3 &&
            $pathParts[0] === 'flights' &&
            $pathParts[1] === 'poleti') {
            $logger->info('Virtual route candidate match found');
            return true;
        }

        $logger->info('Pattern does not match', [
            'first' => $pathParts[0] ?? 'none',
            'second' => $pathParts[1] ?? 'none'
        ]);

        return false;
    }

    /**
     * Check if a real page exists at the given path
     * This prevents virtual route processing of actual pages
     */
    private function pageExistsAtPath(string $path, Site $site): bool
    {
        try {
            // Simple check: if we can resolve this path to a page, it's a real page
            $siteRootPageId = $site->getRootPageId();

            // For now, use a simple heuristic: if the path has more than 3 segments,
            // it's likely a virtual route. This is a conservative approach.
            $segments = array_filter(explode('/', trim($path, '/')));

            // If it's exactly 2 segments and starts with known patterns, it might be virtual
            // If it's more than 2 segments, it's likely virtual
            // If it's 1 segment, it's definitely a real page
            return count($segments) <= 1;

        } catch (\Exception $e) {
            // If we can't determine, err on the side of caution and don't process
            return true;
        }
    }



    /**
     * Get the path for the template page that PageResolver can find
     */
    private function getTemplatePagePath(array $templatePage, Site $site): string
    {
        // Use the template page slug
        $slug = $templatePage['slug'] ?? '';

        // Ensure it starts with the site base path
        $basePath = rtrim($site->getBase()->getPath(), '/');

        if (strpos($slug, $basePath) !== 0) {
            $slug = $basePath . '/' . ltrim($slug, '/');
        }

        return $slug;
    }
}
